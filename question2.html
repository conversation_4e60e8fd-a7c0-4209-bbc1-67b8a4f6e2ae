<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>السؤال الثاني - امتحان الكتاب المقدس</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: '<PERSON><PERSON>', serif;
            background: linear-gradient(135deg, #f5f5dc 0%, #fff8dc 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            padding: 40px;
            position: relative;
            overflow: hidden;
        }

        .container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, gold, #daa520, gold);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .question-number {
            font-size: 24px;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .progress-bar {
            width: 100%;
            height: 10px;
            background: #e0e0e0;
            border-radius: 10px;
            margin-bottom: 20px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #45a049);
            border-radius: 10px;
            transition: width 0.3s ease;
        }

        .question-container {
            background: #f8f9fa;
            border: 2px solid #ddd;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            text-align: center;
        }

        .question-text {
            font-size: 24px;
            color: #2c3e50;
            margin-bottom: 30px;
            font-weight: 600;
        }

        .answers-container {
            display: grid;
            gap: 15px;
            margin-bottom: 30px;
        }

        .answer-option {
            background: #fff;
            border: 2px solid #ddd;
            border-radius: 15px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 18px;
            color: #333;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            gap: 15px;
        }

        .answer-option:hover {
            border-color: #4CAF50;
            background: #f0f8ff;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(76, 175, 80, 0.2);
        }

        .answer-option.selected {
            border-color: #4CAF50;
            background: #e8f5e9;
            color: #2e7d32;
        }

        .answer-letter {
            font-weight: 700;
            color: #666;
            min-width: 30px;
        }

        .answer-option.selected .answer-letter {
            color: #2e7d32;
        }

        .navigation {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 30px;
        }

        .nav-button {
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 120px;
        }

        .next-button {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
        }

        .next-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
        }

        .next-button:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .question-counter {
            font-size: 18px;
            color: #666;
            font-weight: 600;
        }

        .timer {
            background: #fff3cd;
            border: 2px solid #ffc107;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
            text-align: center;
            font-size: 16px;
            color: #856404;
        }

        @media (max-width: 600px) {
            .container {
                padding: 20px;
                margin: 10px;
            }
            
            .question-text {
                font-size: 20px;
            }
            
            .answer-option {
                padding: 15px;
                font-size: 16px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="question-number">السؤال الثاني</div>
            <div class="progress-bar">
                <div class="progress-fill" style="width: 67%"></div>
            </div>
        </div>

        <div class="timer">
            ⏰ وقت الامتحان: <span id="timer">غير محدود</span>
        </div>

        <div class="question-container">
            <div class="question-text">
                🔹 2. في أي موضع وُلد السيد المسيح؟
            </div>
            
            <div class="answers-container">
                <div class="answer-option" data-answer="أ">
                    <span class="answer-letter">أ)</span>
                    <span>أورشليم</span>
                </div>
                <div class="answer-option" data-answer="ب">
                    <span class="answer-letter">ب)</span>
                    <span>الناصرة</span>
                </div>
                <div class="answer-option" data-answer="ج">
                    <span class="answer-letter">ج)</span>
                    <span>بيت لحم</span>
                </div>
                <div class="answer-option" data-answer="د">
                    <span class="answer-letter">د)</span>
                    <span>قانا</span>
                </div>
            </div>
        </div>

        <div class="navigation">
            <div class="question-counter">2 من 3</div>
            <button class="nav-button next-button" id="nextButton" disabled>
                السؤال التالي ←
            </button>
        </div>
    </div>

    <script>
        // بيانات السؤال
        const questionData = {
            number: 2,
            correctAnswer: 'ج',
            totalQuestions: 3
        };

        let selectedAnswer = null;

        // إضافة event listeners للإجابات
        document.querySelectorAll('.answer-option').forEach(option => {
            option.addEventListener('click', function() {
                // إزالة التحديد من جميع الإجابات
                document.querySelectorAll('.answer-option').forEach(opt => {
                    opt.classList.remove('selected');
                });
                
                // تحديد الإجابة الحالية
                this.classList.add('selected');
                selectedAnswer = this.dataset.answer;
                
                // تفعيل زر السؤال التالي
                document.getElementById('nextButton').disabled = false;
            });
        });

        // زر السؤال التالي
        document.getElementById('nextButton').addEventListener('click', function() {
            if (selectedAnswer) {
                // حفظ الإجابة
                let answers = JSON.parse(localStorage.getItem('answers') || '{}');
                answers[questionData.number] = selectedAnswer;
                localStorage.setItem('answers', JSON.stringify(answers));
                
                // حساب النتيجة
                let score = parseInt(localStorage.getItem('score') || '0');
                if (selectedAnswer === questionData.correctAnswer) {
                    score += 1;
                    localStorage.setItem('score', score.toString());
                }
                
                // الانتقال للسؤال التالي
                if (questionData.number < questionData.totalQuestions) {
                    localStorage.setItem('currentQuestion', (questionData.number + 1).toString());
                    window.location.href = `question${questionData.number + 1}.html`;
                } else {
                    // انتهاء الامتحان
                    window.location.href = 'results.html';
                }
            }
        });

        // التحقق من بدء الامتحان
        if (localStorage.getItem('examStarted') !== 'true') {
            alert('يجب البدء من الصفحة الرئيسية!');
            window.location.href = 'exam_page.html';
        }
    </script>
</body>
</html>