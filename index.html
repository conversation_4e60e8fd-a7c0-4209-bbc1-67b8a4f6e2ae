<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>استعد للامتحان... قلبًا وعقلاً</title>
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #f5f3f0 0%, #faf8f5 50%, #f0ede8 100%);
            min-height: 100vh;
            padding: 20px;
            color: #2c3e50;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            border: 2px solid #d4af37;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }

        .verse {
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            font-family: 'Amiri', serif;
            font-size: 22px;
            line-height: 1.8;
            text-align: center;
            color: white;
            font-weight: 600;
        }

        .main-title {
            font-size: 2.5em;
            font-weight: 700;
            margin: 20px 0;
            color: #d4af37;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .content {
            padding: 40px;
        }

        .timer-section {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin: 30px 0;
            text-align: center;
        }

        .timer-display {
            font-size: 3em;
            font-weight: 700;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
        }

        .exam-button {
            text-align: center;
            margin: 40px 0;
        }

        .btn {
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
            color: white;
            padding: 15px 40px;
            border: none;
            border-radius: 50px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(39, 174, 96, 0.3);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(39, 174, 96, 0.4);
        }

        .btn:disabled {
            background: #95a5a6;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .exam-section {
            display: none;
            padding: 20px;
        }

        .question {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }

        .question h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.1em;
        }

        .options {
            list-style: none;
            padding: 0;
        }

        .options li {
            margin: 10px 0;
            padding: 10px;
            background: white;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .options li:hover {
            background: #e3f2fd;
            border-color: #2196f3;
        }

        .options li.correct {
            background: #d4edda;
            border-color: #28a745;
            color: #155724;
        }

        .options li.selected {
            background: #fff3cd;
            border-color: #ffc107;
        }

        .fill-blank {
            background: white;
            border: 2px solid #007bff;
            border-radius: 5px;
            padding: 8px;
            margin: 0 5px;
            min-width: 100px;
            text-align: center;
        }

        .true-false {
            display: flex;
            gap: 20px;
            justify-content: center;
            margin: 15px 0;
        }

        .true-false label {
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            padding: 10px 20px;
            border: 2px solid #dee2e6;
            border-radius: 25px;
            transition: all 0.3s ease;
        }

        .true-false label:hover {
            background: #e3f2fd;
            border-color: #2196f3;
        }

        .true-false input[type="radio"] {
            margin: 0;
        }

        .circle-options {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin: 15px 0;
        }

        .circle-option {
            width: 40px;
            height: 40px;
            border: 3px solid #dee2e6;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: bold;
        }

        .circle-option:hover {
            border-color: #2196f3;
            background: #e3f2fd;
        }

        .circle-option.selected {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }

        .submit-btn {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            padding: 15px 50px;
            border: none;
            border-radius: 50px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            margin: 30px auto;
            display: block;
        }

        .hidden {
            display: none;
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 15px;
            }
            
            .main-title {
                font-size: 2em;
            }
            
            .content {
                padding: 20px;
            }

            .timer-display {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="verse">
                "افحصني يا الله واعرف قلبي. امتحني واعرف أفكاري."<br>
                <small>(مزمور 139: 23)</small>
            </div>
            <h1 class="main-title">استعد للامتحان... قلبًا وعقلاً</h1>
        </div>

        <div class="content">
            <div id="welcomeSection">
                <div class="timer-section">
                    <h2>⏰ العداد التنازلي للامتحان</h2>
                    <div class="timer-display" id="timerDisplay">02:00:00</div>
                    <p>سيتم فتح الامتحان تلقائياً بعد انتهاء العداد</p>
                </div>

                <div class="exam-button">
                    <button class="btn" id="examButton" disabled onclick="startExam()">
                        ⏳ انتظر انتهاء العداد
                    </button>
                </div>
            </div>

            <div id="examSection" class="exam-section">
                <h2 style="text-align: center; color: #2c3e50; margin-bottom: 30px;">
                    امتحان: أسئلة في العقيدة المسيحية (مستوى متوسط)
                </h2>
                
                <!-- أسئلة الاختيار من متعدد -->
                <div class="question">
                    <h3>1. من هو التلميذ الذي أنكر المسيح ثلاث مرات؟</h3>
                    <ul class="options">
                        <li onclick="selectOption(this)">أ) يوحنا</li>
                        <li onclick="selectOption(this)" class="correct">ب) بطرس ✅</li>
                        <li onclick="selectOption(this)">ج) توما</li>
                        <li onclick="selectOption(this)">د) متى</li>
                    </ul>
                </div>

                <div class="question">
                    <h3>2. في أي موضع وُلد السيد المسيح؟</h3>
                    <ul class="options">
                        <li onclick="selectOption(this)">أ) أورشليم</li>
                        <li onclick="selectOption(this)">ب) الناصرة</li>
                        <li onclick="selectOption(this)" class="correct">ج) بيت لحم ✅</li>
                        <li onclick="selectOption(this)">د) قانا</li>
                    </ul>
                </div>

                <div class="question">
                    <h3>3. ما اسم الجبل الذي صُلب عليه السيد المسيح؟</h3>
                    <ul class="options">
                        <li onclick="selectOption(this)">أ) جبل الزيتون</li>
                        <li onclick="selectOption(this)">ب) جبل التجلي</li>
                        <li onclick="selectOption(this)" class="correct">ج) جلجثة ✅</li>
                        <li onclick="selectOption(this)">د) الكرمل</li>
                    </ul>
                </div>

                <div class="question">
                    <h3>4. كم عدد وصايا العهد القديم؟</h3>
                    <ul class="options">
                        <li onclick="selectOption(this)">أ) 5</li>
                        <li onclick="selectOption(this)">ب) 7</li>
                        <li onclick="selectOption(this)" class="correct">ج) 10 ✅</li>
                        <li onclick="selectOption(this)">د) 12</li>
                    </ul>
                </div>

                <div class="question">
                    <h3>5. من كتب سفر الرؤيا؟</h3>
                    <ul class="options">
                        <li onclick="selectOption(this)">أ) بولس</li>
                        <li onclick="selectOption(this)">ب) بطرس</li>
                        <li onclick="selectOption(this)" class="correct">ج) يوحنا ✅</li>
                        <li onclick="selectOption(this)">د) لوقا</li>
                    </ul>
                </div>

                <div class="question">
                    <h3>6. من هو التلميذ الذي شَكَّ في قيامة المسيح؟</h3>
                    <ul class="options">
                        <li onclick="selectOption(this)" class="correct">أ) توما ✅</li>
                        <li onclick="selectOption(this)">ب) فيليب</li>
                        <li onclick="selectOption(this)">ج) متى</li>
                        <li onclick="selectOption(this)">د) يعقوب</li>
                    </ul>
                </div>

                <div class="question">
                    <h3>7. من هي أول من رأت المسيح بعد القيامة؟</h3>
                    <ul class="options">
                        <li onclick="selectOption(this)" class="correct">أ) مريم المجدلية ✅</li>
                        <li onclick="selectOption(this)">ب) أم يسوع</li>
                        <li onclick="selectOption(this)">ج) حنة النبية</li>
                        <li onclick="selectOption(this)">د) مرثا</li>
                    </ul>
                </div>

                <div class="question">
                    <h3>8. كم عدد الأناجيل القانونية؟</h3>
                    <ul class="options">
                        <li onclick="selectOption(this)">أ) ثلاثة</li>
                        <li onclick="selectOption(this)" class="correct">ب) أربعة ✅</li>
                        <li onclick="selectOption(this)">ج) خمسة</li>
                        <li onclick="selectOption(this)">د) سبعة</li>
                    </ul>
                </div>

                <div class="question">
                    <h3>9. من هو الذي حمل صليب المسيح؟</h3>
                    <ul class="options">
                        <li onclick="selectOption(this)">أ) بطرس</li>
                        <li onclick="selectOption(this)" class="correct">ب) سمعان القيرواني ✅</li>
                        <li onclick="selectOption(this)">ج) يهوذا</li>
                        <li onclick="selectOption(this)">د) لوقا</li>
                    </ul>
                </div>

                <div class="question">
                    <h3>10. أين تم عماد السيد المسيح؟</h3>
                    <ul class="options">
                        <li onclick="selectOption(this)" class="correct">أ) نهر الأردن ✅</li>
                        <li onclick="selectOption(this)">ب) بحر الجليل</li>
                        <li onclick="selectOption(this)">ج) بحيرة طبرية</li>
                        <li onclick="selectOption(this)">د) نهر النيل</li>
                    </ul>
                </div>

                <!-- المزيد من أسئلة الاختيار من متعدد -->
                <div class="question">
                    <h3>11. كم عدد أسرار الكنيسة؟</h3>
                    <ul class="options">
                        <li onclick="selectOption(this)">أ) 5</li>
                        <li onclick="selectOption(this)">ب) 6</li>
                        <li onclick="selectOption(this)" class="correct">ج) 7 ✅</li>
                        <li onclick="selectOption(this)">د) 10</li>
                    </ul>
                </div>

                <div class="question">
                    <h3>12. من هو الذي خان المسيح؟</h3>
                    <ul class="options">
                        <li onclick="selectOption(this)">أ) بطرس</li>
                        <li onclick="selectOption(this)">ب) توما</li>
                        <li onclick="selectOption(this)" class="correct">ج) يهوذا ✅</li>
                        <li onclick="selectOption(this)">د) فيليبس</li>
                    </ul>
                </div>

                <div class="question">
                    <h3>13. ما هو أول معجزة صنعها المسيح؟</h3>
                    <ul class="options">
                        <li onclick="selectOption(this)">أ) شفاء الأعمى</li>
                        <li onclick="selectOption(this)">ب) إقامة لعازر</li>
                        <li onclick="selectOption(this)" class="correct">ج) تحويل الماء إلى خمر ✅</li>
                        <li onclick="selectOption(this)">د) إشباع الجموع</li>
                    </ul>
                </div>

                <div class="question">
                    <h3>14. كم عدد المزامير في الكتاب المقدس؟</h3>
                    <ul class="options">
                        <li onclick="selectOption(this)">أ) 100</li>
                        <li onclick="selectOption(this)">ب) 121</li>
                        <li onclick="selectOption(this)" class="correct">ج) 150 ✅</li>
                        <li onclick="selectOption(this)">د) 200</li>
                    </ul>
                </div>

                <div class="question">
                    <h3>15. من هو النبي الذي صعد للسماء في مركبة نارية؟</h3>
                    <ul class="options">
                        <li onclick="selectOption(this)" class="correct">أ) إيليا ✅</li>
                        <li onclick="selectOption(this)">ب) إشعياء</li>
                        <li onclick="selectOption(this)">ج) حزقيال</li>
                        <li onclick="selectOption(this)">د) هوشع</li>
                    </ul>
                </div>

                <!-- أسئلة أكمل -->
                <div class="question">
                    <h3>31. أكمل: "في البدء خلق الله <input type="text" class="fill-blank" placeholder="..."> و<input type="text" class="fill-blank" placeholder="...">."</h3>
                    <p style="color: #28a745; margin-top: 10px;">✅ الإجابة الصحيحة: السماء والأرض</p>
                </div>

                <div class="question">
                    <h3>32. أكمل: "أنا هو الطريق و<input type="text" class="fill-blank" placeholder="..."> و<input type="text" class="fill-blank" placeholder="...">, ليس أحد يأتي إلى الآب إلا بي."</h3>
                    <p style="color: #28a745; margin-top: 10px;">✅ الإجابة الصحيحة: الحق والحياة</p>
                </div>

                <div class="question">
                    <h3>33. أكمل: "مغبوط هو العطاء <input type="text" class="fill-blank" placeholder="..."> من الأخذ."</h3>
                    <p style="color: #28a745; margin-top: 10px;">✅ الإجابة الصحيحة: أكثر</p>
                </div>

                <!-- أسئلة صح وخطأ -->
                <div class="question">
                    <h3>37. يوحنا المعمدان كتب سفر الرؤيا.</h3>
                    <div class="true-false">
                        <label>
                            <input type="radio" name="q37" value="true">
                            ✅ صح
                        </label>
                        <label>
                            <input type="radio" name="q37" value="false" checked>
                            ❌ خطأ (الصحيح: يوحنا الحبيب)
                        </label>
                    </div>
                </div>

                <div class="question">
                    <h3>38. السيد المسيح قام من بين الأموات في اليوم الثالث.</h3>
                    <div class="true-false">
                        <label>
                            <input type="radio" name="q38" value="true" checked>
                            ✅ صح
                        </label>
                        <label>
                            <input type="radio" name="q38" value="false">
                            ❌ خطأ
                        </label>
                    </div>
                </div>

                <div class="question">
                    <h3>39. عدد أسرار الكنيسة في التقليد الأرثوذكسي هو 5.</h3>
                    <div class="true-false">
                        <label>
                            <input type="radio" name="q39" value="true">
                            ✅ صح
                        </label>
                        <label>
                            <input type="radio" name="q39" value="false" checked>
                            ❌ خطأ (الصحيح: 7)
                        </label>
                    </div>
                </div>

                <div class="question">
                    <h3>40. صُلب السيد المسيح على جبل الزيتون.</h3>
                    <div class="true-false">
                        <label>
                            <input type="radio" name="q40" value="true">
                            ✅ صح
                        </label>
                        <label>
                            <input type="radio" name="q40" value="false" checked>
                            ❌ خطأ (الصحيح: جبل الجلجثة)
                        </label>
                    </div>
                </div>

                <div class="question">
                    <h3>41. بولس الرسول كان من الاثني عشر تلميذًا.</h3>
                    <div class="true-false">
                        <label>
                            <input type="radio" name="q41" value="true">
                            ✅ صح
                        </label>
                        <label>
                            <input type="radio" name="q41" value="false" checked>
                            ❌ خطأ (بولس لم يكن من الاثني عشر)
                        </label>
                    </div>
                </div>

                <div class="question">
                    <h3>42. العهد الجديد كُتب باليونانية في الأصل.</h3>
                    <div class="true-false">
                        <label>
                            <input type="radio" name="q42" value="true" checked>
                            ✅ صح
                        </label>
                        <label>
                            <input type="radio" name="q42" value="false">
                            ❌ خطأ
                        </label>
                    </div>
                </div>

                <!-- أسئلة ظلل الدائرة -->
                <div class="question">
                    <h3>43. أين وُلد السيد المسيح؟</h3>
                    <div class="circle-options">
                        <div class="circle-option" onclick="selectCircle(this, 'q43')">ن</div>
                        <div class="circle-option" onclick="selectCircle(this, 'q43')">أ</div>
                        <div class="circle-option selected" onclick="selectCircle(this, 'q43')">ب</div>
                        <div class="circle-option" onclick="selectCircle(this, 'q43')">ق</div>
                    </div>
                    <p style="text-align: center; margin-top: 10px;">الناصرة - أورشليم - <span style="color: #28a745;">بيت لحم ✅</span> - قانا</p>
                </div>

                <div class="question">
                    <h3>44. من هو التلميذ الذي شَكَّ في القيامة؟</h3>
                    <div class="circle-options">
                        <div class="circle-option" onclick="selectCircle(this, 'q44')">ب</div>
                        <div class="circle-option selected" onclick="selectCircle(this, 'q44')">ت</div>
                        <div class="circle-option" onclick="selectCircle(this, 'q44')">م</div>
                        <div class="circle-option" onclick="selectCircle(this, 'q44')">ي</div>
                    </div>
                    <p style="text-align: center; margin-top: 10px;">بطرس - <span style="color: #28a745;">توما ✅</span> - متى - يوحنا</p>
                </div>

                <div class="question">
                    <h3>45. كم عدد العذارى في مثل العذارى الحكيمات؟</h3>
                    <div class="circle-options">
                        <div class="circle-option" onclick="selectCircle(this, 'q45')">5</div>
                        <div class="circle-option" onclick="selectCircle(this, 'q45')">7</div>
                        <div class="circle-option selected" onclick="selectCircle(this, 'q45')">10</div>
                        <div class="circle-option" onclick="selectCircle(this, 'q45')">12</div>
                    </div>
                    <p style="text-align: center; margin-top: 10px;">5 - 7 - <span style="color: #28a745;">10 ✅</span> - 12</p>
                </div>

                <div class="question">
                    <h3>46. ماذا يُرمز به الزيت في الكتاب المقدس؟</h3>
                    <div class="circle-options">
                        <div class="circle-option" onclick="selectCircle(this, 'q46')">خ</div>
                        <div class="circle-option selected" onclick="selectCircle(this, 'q46')">ر</div>
                        <div class="circle-option" onclick="selectCircle(this, 'q46')">م</div>
                        <div class="circle-option" onclick="selectCircle(this, 'q46')">ب</div>
                    </div>
                    <p style="text-align: center; margin-top: 10px;">الخطيئة - <span style="color: #28a745;">الروح القدس ✅</span> - الماء - البركة</p>
                </div>

                <div class="question">
                    <h3>47. ما هو أول سر في الكنيسة؟</h3>
                    <div class="circle-options">
                        <div class="circle-option selected" onclick="selectCircle(this, 'q47')">م</div>
                        <div class="circle-option" onclick="selectCircle(this, 'q47')">ت</div>
                        <div class="circle-option" onclick="selectCircle(this, 'q47')">ز</div>
                        <div class="circle-option" onclick="selectCircle(this, 'q47')">ك</div>
                    </div>
                    <p style="text-align: center; margin-top: 10px;"><span style="color: #28a745;">المعمودية ✅</span> - التناول - الزواج - الكهنوت</p>
                </div>

                <button class="submit-btn" onclick="submitExam()">تسليم الامتحان</button>
            </div>
        </div>
    </div>

    <script>
        // العداد التنازلي - ساعتين (7200 ثانية)
        let timeLeft = 7200;
        let timerInterval;

        // التحقق من وجود وقت محفوظ في localStorage
        const savedTime = localStorage.getItem('examTimer');
        const savedStartTime = localStorage.getItem('examStartTime');

        if (savedTime && savedStartTime) {
            const currentTime = new Date().getTime();
            const startTime = parseInt(savedStartTime);
            const elapsedSeconds = Math.floor((currentTime - startTime) / 1000);
            timeLeft = Math.max(0, 7200 - elapsedSeconds);
        } else {
            // حفظ وقت البداية
            localStorage.setItem('examStartTime', new Date().getTime().toString());
        }

        function updateTimer() {
            const hours = Math.floor(timeLeft / 3600);
            const minutes = Math.floor((timeLeft % 3600) / 60);
            const seconds = timeLeft % 60;

            const display = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            document.getElementById('timerDisplay').textContent = display;

            if (timeLeft <= 0) {
                clearInterval(timerInterval);
                enableExam();
            } else {
                timeLeft--;
                localStorage.setItem('examTimer', timeLeft.toString());
            }
        }

        function enableExam() {
            const examButton = document.getElementById('examButton');
            examButton.disabled = false;
            examButton.textContent = '📖 ابدأ الامتحان الآن';
            examButton.style.background = 'linear-gradient(135deg, #27ae60 0%, #2ecc71 100%)';
            
            // إظهار رسالة
            alert('🙏 انتهى وقت الانتظار!\nيمكنك الآن بدء الامتحان\nالرب معك!');
        }

        function startExam() {
            if (timeLeft > 0) {
                alert('⏳ يرجى انتظار انتهاء العداد أولاً');
                return;
            }

            document.getElementById('welcomeSection').style.display = 'none';
            document.getElementById('examSection').style.display = 'block';
            
            alert('🙏 بسم الآب والابن والروح القدس الإله الواحد آمين.\n\nبدء الامتحان...\nالرب معك!');
        }

        function selectOption(element) {
            // إزالة التحديد من باقي الخيارات في نفس السؤال
            const siblings = element.parentNode.children;
            for (let sibling of siblings) {
                sibling.classList.remove('selected');
            }
            // تحديد الخيار المختار
            element.classList.add('selected');
        }

        function selectCircle(element, questionName) {
            // إزالة التحديد من باقي الدوائر في نفس السؤال
            const siblings = element.parentNode.children;
            for (let sibling of siblings) {
                sibling.classList.remove('selected');
            }
            // تحديد الدائرة المختارة
            element.classList.add('selected');
        }

        function submitExam() {
            if (confirm('هل أنت متأكد من تسليم الامتحان؟\nلن تتمكن من العودة بعد التسليم.')) {
                alert('🙏 تم تسليم الامتحان بنجاح!\nالرب يبارك تعبك ومجهودك.\nبالتوفيق! ✨');
                
                // مسح البيانات المحفوظة
                localStorage.removeItem('examTimer');
                localStorage.removeItem('examStartTime');
                
                // يمكن إضافة كود لإرسال الإجابات هنا
                // window.location.href = 'thank-you.html';
            }
        }

        // بدء العداد
        if (timeLeft > 0) {
            timerInterval = setInterval(updateTimer, 1000);
            updateTimer(); // تحديث فوري
        } else {
            enableExam();
        }

        // منع إغلاق الصفحة بدون تأكيد أثناء الامتحان
        window.addEventListener('beforeunload', function(e) {
            if (document.getElementById('examSection').style.display !== 'none') {
                e.preventDefault();
                e.returnValue = 'هل أنت متأكد من مغادرة الامتحان؟';
            }
        });
    </script>
</body>
</html>
