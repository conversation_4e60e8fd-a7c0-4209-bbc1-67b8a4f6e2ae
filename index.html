<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>استعد للامتحان... قلبًا وعقلاً</title>
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #f5f3f0 0%, #faf8f5 50%, #f0ede8 100%);
            min-height: 100vh;
            padding: 20px;
            color: #2c3e50;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            border: 2px solid #d4af37;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }

        .header::before {
            content: '✝️';
            position: absolute;
            top: 15px;
            right: 30px;
            font-size: 24px;
            opacity: 0.7;
        }

        .verse {
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            font-family: 'Amiri', serif;
            font-size: 22px;
            line-height: 1.8;
            text-align: center;
            color: white;
            font-weight: 600;
        }

        .main-title {
            font-size: 2.5em;
            font-weight: 700;
            margin: 20px 0;
            color: #d4af37;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .content {
            padding: 40px;
        }

        .intro {
            background: rgba(52, 152, 219, 0.1);
            border-right: 5px solid #3498db;
            padding: 25px;
            margin: 30px 0;
            border-radius: 10px;
            font-size: 16px;
            line-height: 1.6;
        }

        .exam-schedule {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin: 30px 0;
            text-align: center;
        }

        .schedule-title {
            font-size: 1.5em;
            font-weight: 600;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .time-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }

        .time-box {
            background: rgba(255, 255, 255, 0.2);
            padding: 15px;
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .warning {
            background: rgba(231, 76, 60, 0.1);
            border: 2px solid #e74c3c;
            color: #c0392b;
            padding: 15px;
            border-radius: 10px;
            margin-top: 15px;
            font-weight: 600;
        }

        .motivation {
            background: rgba(155, 89, 182, 0.1);
            border-right: 5px solid #9b59b6;
            padding: 25px;
            margin: 30px 0;
            border-radius: 10px;
            font-size: 16px;
            line-height: 1.6;
        }

        .notes {
            background: rgba(241, 196, 15, 0.1);
            border: 2px solid #f1c40f;
            padding: 25px;
            border-radius: 15px;
            margin: 30px 0;
        }

        .notes h3 {
            color: #f39c12;
            margin-bottom: 15px;
            font-size: 1.3em;
        }

        .notes ul {
            list-style: none;
            padding: 0;
        }

        .notes li {
            padding: 8px 0;
            border-bottom: 1px solid rgba(241, 196, 15, 0.2);
            position: relative;
            padding-right: 25px;
        }

        .notes li::before {
            content: '📌';
            position: absolute;
            right: 0;
            top: 8px;
        }

        .final-message {
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            margin: 30px 0;
            font-size: 16px;
            line-height: 1.8;
        }

        .exam-button {
            text-align: center;
            margin: 40px 0;
        }

        .btn {
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
            color: white;
            padding: 15px 40px;
            border: none;
            border-radius: 50px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(39, 174, 96, 0.3);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(39, 174, 96, 0.4);
        }

        .btn:disabled {
            background: #95a5a6;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .current-time {
            text-align: center;
            margin: 20px 0;
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
        }

        .icons {
            text-align: center;
            margin: 20px 0;
            font-size: 24px;
        }

        .icons span {
            margin: 0 15px;
            opacity: 0.7;
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 15px;
            }
            
            .main-title {
                font-size: 2em;
            }
            
            .time-info {
                grid-template-columns: 1fr;
            }
            
            .content {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="verse">
                "افحصني يا الله واعرف قلبي. امتحني واعرف أفكاري."<br>
                <small>(مزمور 139: 23)</small>
            </div>
            <h1 class="main-title">استعد للامتحان... قلبًا وعقلاً</h1>
        </div>

        <div class="content">
            <div class="intro">
                <strong>🕊️ هذه الصفحة مخصصة للطلبة المسيحيين فقط.</strong><br>
                يرجى تجهيز نفسك بهدوء وصلاة قبل الدخول إلى الامتحان.<br>
                الرب معك في كل خطوة 🙏
            </div>

            <div class="current-time">
                ⏰ الوقت الحالي: <span id="currentTime"></span>
            </div>

            <div class="exam-schedule">
                <div class="schedule-title">
                    📅 موعد الامتحان
                </div>
                <div class="time-info">
                    <div class="time-box">
                        <strong>🕔 موعد فتح الامتحان</strong><br>
                        الساعة 5:00 مساءً
                    </div>
                    <div class="time-box">
                        <strong>⏳ موعد غلق الامتحان</strong><br>
                        الساعة 12:30 صباحًا
                    </div>
                </div>
                <div class="warning">
                    ⚠️ لن يُسمح بالدخول بعد هذا الوقت.
                </div>
            </div>

            <div class="motivation">
                <strong>❓ هل أنت مستعد أن تُظهر معرفتك، وإيمانك، وثقتك؟</strong><br>
                هذا الامتحان ليس فقط اختبارًا للمعلومات، بل هو شهادة لإيمانك أيضًا.
            </div>

            <div class="notes">
                <h3>📌 ملاحظات مهمة:</h3>
                <ul>
                    <li>تأكد من اتصال الإنترنت</li>
                    <li>افتح الامتحان من جهازك الشخصي فقط</li>
                    <li>لا تشارك الرابط مع أي شخص آخر</li>
                    <li>وقت الامتحان محدود، ولن يمكن الرجوع بعد الخروج</li>
                </ul>
            </div>

            <div class="exam-button">
                <button class="btn" id="examButton" onclick="startExam()">
                    📖 ابدأ الامتحان
                </button>
            </div>

            <div class="icons">
                <span>⏰</span>
                <span>✝️</span>
                <span>📖</span>
            </div>

            <div class="final-message">
                <strong>✨ الرب يراك ويعرف تعبك...</strong><br>
                ثق أنه سيكون معك، يرشدك، ويذكّرك، ويقوّيك.<br>
                <strong>بالتوفيق يا بطل الإيمان! 🙏</strong>
            </div>
        </div>
    </div>

    <script>
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('ar-EG', {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: true
            });
            document.getElementById('currentTime').textContent = timeString;
        }

        function checkExamTime() {
            const now = new Date();
            const currentHour = now.getHours();
            const currentMinute = now.getMinutes();
            
            // Convert to minutes for easier comparison
            const currentTimeInMinutes = currentHour * 60 + currentMinute;
            const examStartTime = 17 * 60; // 5:00 PM
            const examEndTime = 0 * 60 + 30; // 12:30 AM (next day)
            
            const examButton = document.getElementById('examButton');
            
            // Check if current time is between 5:00 PM and 12:30 AM
            if (currentTimeInMinutes >= examStartTime || currentTimeInMinutes <= examEndTime) {
                examButton.disabled = false;
                examButton.textContent = '📖 ابدأ الامتحان';
            } else {
                examButton.disabled = true;
                examButton.textContent = '⏳ الامتحان غير متاح حالياً';
            }
        }

        function startExam() {
            const now = new Date();
            const currentHour = now.getHours();
            const currentMinute = now.getMinutes();
            const currentTimeInMinutes = currentHour * 60 + currentMinute;
            const examStartTime = 17 * 60; // 5:00 PM
            const examEndTime = 0 * 60 + 30; // 12:30 AM (next day)
            
            if (currentTimeInMinutes >= examStartTime || currentTimeInMinutes <= examEndTime) {
                alert('🙏 بسم الآب والابن والروح القدس الإله الواحد آمين.\n\nسيتم توجيهك إلى صفحة الامتحان...\nالرب معك!');
                // هنا يمكن إضافة رابط الامتحان الفعلي
                // window.location.href = 'exam.html';
            } else {
                alert('⚠️ عذراً، الامتحان متاح فقط من الساعة 5:00 مساءً حتى 12:30 صباحاً');
            }
        }

        // Update time every second
        setInterval(updateTime, 1000);
        setInterval(checkExamTime, 1000);
        
        // Initial calls
        updateTime();
        checkExamTime();
    </script>
</body>
</html>
