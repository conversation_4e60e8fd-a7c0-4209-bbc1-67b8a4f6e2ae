// بيانات جميع الأسئلة
const questions = [
    {
        number: 1,
        text: "من هو التلميذ الذي أنكر المسيح ثلاث مرات؟",
        answers: ["يوحنا", "بطرس", "توما", "متى"],
        correct: "ب"
    },
    {
        number: 2,
        text: "في أي موضع وُلد السيد المسيح؟",
        answers: ["أورشليم", "الناصرة", "بيت لحم", "قانا"],
        correct: "ج"
    },
    {
        number: 3,
        text: "ما اسم الجبل الذي صُلب عليه السيد المسيح؟",
        answers: ["جبل الزيتون", "جبل التجلي", "جلجثة", "الكرمل"],
        correct: "ج"
    },
    {
        number: 4,
        text: "كم عدد وصايا العهد القديم؟",
        answers: ["5", "7", "10", "12"],
        correct: "ج"
    },
    {
        number: 5,
        text: "من كتب سفر الرؤيا؟",
        answers: ["بولس", "بطرس", "يوحنا", "لوقا"],
        correct: "ج"
    },
    {
        number: 6,
        text: "من هو التلميذ الذي شَكَّ في قيامة المسيح؟",
        answers: ["توما", "فيليب", "متى", "يعقوب"],
        correct: "أ"
    },
    {
        number: 7,
        text: "من هي أول من رأت المسيح بعد القيامة؟",
        answers: ["مريم المجدلية", "أم يسوع", "حنة النبية", "مرثا"],
        correct: "أ"
    },
    {
        number: 8,
        text: "كم عدد الأناجيل القانونية؟",
        answers: ["ثلاثة", "أربعة", "خمسة", "سبعة"],
        correct: "ب"
    },
    {
        number: 9,
        text: "من هو الذي حمل صليب المسيح؟",
        answers: ["بطرس", "سمعان القيرواني", "يهوذا", "لوقا"],
        correct: "ب"
    },
    {
        number: 10,
        text: "أين تم عماد السيد المسيح؟",
        answers: ["نهر الأردن", "بحر الجليل", "بحيرة طبرية", "نهر النيل"],
        correct: "أ"
    },
    {
        number: 11,
        text: "كم عدد أسرار الكنيسة؟",
        answers: ["5", "6", "7", "10"],
        correct: "ج"
    },
    {
        number: 12,
        text: "ما هو أقدم سفر في الكتاب المقدس؟",
        answers: ["التكوين", "أيوب", "الخروج", "يشوع"],
        correct: "ب"
    },
    {
        number: 13,
        text: "ما هي اللغة الأصلية للعهد الجديد؟",
        answers: ["العبرية", "الآرامية", "اليونانية", "اللاتينية"],
        correct: "ج"
    },
    {
        number: 14,
        text: "من هو أول ملك لإسرائيل؟",
        answers: ["داود", "شاول", "سليمان", "رحبعام"],
        correct: "ب"
    },
    {
        number: 15,
        text: "ما هو اسم البحر الذي انشق أمام موسى؟",
        answers: ["البحر الأحمر", "البحر المتوسط", "نهر الأردن", "بحر الجليل"],
        correct: "أ"
    },
    {
        number: 16,
        text: "كم عدد أيام الصوم الكبير؟",
        answers: ["40", "50", "55", "60"],
        correct: "ج"
    },
    {
        number: 17,
        text: "من هو النبي الذي ابتلعه الحوت؟",
        answers: ["إيليا", "يونان", "إشعياء", "إرميا"],
        correct: "ب"
    },
    {
        number: 18,
        text: "من هو الرسول الذي كتب أكثر عدد من الرسائل؟",
        answers: ["بطرس", "بولس", "يوحنا", "يعقوب"],
        correct: "ب"
    },
    {
        number: 19,
        text: "من هو الشخص الذي دعاه الله من العليقة؟",
        answers: ["نوح", "موسى", "إبراهيم", "إيليا"],
        correct: "ب"
    },
    {
        number: 20,
        text: "من هو الملك الذي بنى هيكل أورشليم؟",
        answers: ["داود", "شاول", "سليمان", "رحبعام"],
        correct: "ج"
    }
];

// دالة لإنشاء HTML للسؤال
function createQuestionHTML(question) {
    const progress = (question.number / 20) * 100;
    const letters = ['أ', 'ب', 'ج', 'د'];
    
    return `<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>السؤال ${question.number} - امتحان الكتاب المقدس</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Amiri', serif;
            background: linear-gradient(135deg, #f5f5dc 0%, #fff8dc 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            padding: 40px;
            position: relative;
            overflow: hidden;
        }

        .container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, gold, #daa520, gold);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .question-number {
            font-size: 24px;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .progress-bar {
            width: 100%;
            height: 10px;
            background: #e0e0e0;
            border-radius: 10px;
            margin-bottom: 20px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #45a049);
            border-radius: 10px;
            transition: width 0.3s ease;
        }

        .question-container {
            background: #f8f9fa;
            border: 2px solid #ddd;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            text-align: center;
        }

        .question-text {
            font-size: 24px;
            color: #2c3e50;
            margin-bottom: 30px;
            font-weight: 600;
        }

        .answers-container {
            display: grid;
            gap: 15px;
            margin-bottom: 30px;
        }

        .answer-option {
            background: #fff;
            border: 2px solid #ddd;
            border-radius: 15px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 18px;
            color: #333;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            gap: 15px;
        }

        .answer-option:hover {
            border-color: #4CAF50;
            background: #f0f8ff;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(76, 175, 80, 0.2);
        }

        .answer-option.selected {
            border-color: #4CAF50;
            background: #e8f5e9;
            color: #2e7d32;
        }

        .answer-letter {
            font-weight: 700;
            color: #666;
            min-width: 30px;
        }

        .answer-option.selected .answer-letter {
            color: #2e7d32;
        }

        .navigation {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 30px;
        }

        .nav-button {
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 120px;
        }

        .next-button {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
        }

        .next-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
        }

        .next-button:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .question-counter {
            font-size: 18px;
            color: #666;
            font-weight: 600;
        }

        .timer {
            background: #fff3cd;
            border: 2px solid #ffc107;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
            text-align: center;
            font-size: 16px;
            color: #856404;
        }

        @media (max-width: 600px) {
            .container {
                padding: 20px;
                margin: 10px;
            }
            
            .question-text {
                font-size: 20px;
            }
            
            .answer-option {
                padding: 15px;
                font-size: 16px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="question-number">السؤال ${question.number}</div>
            <div class="progress-bar">
                <div class="progress-fill" style="width: ${progress}%"></div>
            </div>
        </div>

        <div class="timer">
            ⏰ وقت الامتحان: <span id="timer">غير محدود</span>
        </div>

        <div class="question-container">
            <div class="question-text">
                🔹 ${question.number}. ${question.text}
            </div>
            
            <div class="answers-container">
                ${question.answers.map((answer, index) => `
                <div class="answer-option" data-answer="${letters[index]}">
                    <span class="answer-letter">${letters[index]})</span>
                    <span>${answer}</span>
                </div>
                `).join('')}
            </div>
        </div>

        <div class="navigation">
            <div class="question-counter">${question.number} من 20</div>
            <button class="nav-button next-button" id="nextButton" disabled>
                ${question.number === 20 ? 'إنهاء الامتحان' : 'السؤال التالي'} ←
            </button>
        </div>
    </div>

    <script>
        // بيانات السؤال
        const questionData = {
            number: ${question.number},
            correctAnswer: '${question.correct}',
            totalQuestions: 20
        };

        let selectedAnswer = null;

        // إضافة event listeners للإجابات
        document.querySelectorAll('.answer-option').forEach(option => {
            option.addEventListener('click', function() {
                // إزالة التحديد من جميع الإجابات
                document.querySelectorAll('.answer-option').forEach(opt => {
                    opt.classList.remove('selected');
                });
                
                // تحديد الإجابة الحالية
                this.classList.add('selected');
                selectedAnswer = this.dataset.answer;
                
                // تفعيل زر السؤال التالي
                document.getElementById('nextButton').disabled = false;
            });
        });

        // زر السؤال التالي
        document.getElementById('nextButton').addEventListener('click', function() {
            if (selectedAnswer) {
                // حفظ الإجابة
                let answers = JSON.parse(localStorage.getItem('answers') || '{}');
                answers[questionData.number] = selectedAnswer;
                localStorage.setItem('answers', JSON.stringify(answers));
                
                // حساب النتيجة
                let score = parseInt(localStorage.getItem('score') || '0');
                if (selectedAnswer === questionData.correctAnswer) {
                    score += 1;
                    localStorage.setItem('score', score.toString());
                }
                
                // الانتقال للسؤال التالي
                if (questionData.number < questionData.totalQuestions) {
                    localStorage.setItem('currentQuestion', (questionData.number + 1).toString());
                    window.location.href = \`question\${questionData.number + 1}.html\`;
                } else {
                    // انتهاء الامتحان
                    window.location.href = 'results.html';
                }
            }
        });

        // التحقق من بدء الامتحان
        if (localStorage.getItem('examStarted') !== 'true') {
            alert('يجب البدء من الصفحة الرئيسية!');
            window.location.href = 'exam_page.html';
        }
    </script>
</body>
</html>`;
}

// طباعة جميع الأسئلة
questions.forEach(question => {
    console.log(`=== السؤال ${question.number} ===`);
    console.log(createQuestionHTML(question));
    console.log('\n\n');
});