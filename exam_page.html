<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>استعد للامتحان... قلبًا وعقلاً</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: '<PERSON><PERSON>', serif;
            background: linear-gradient(135deg, #f5f5dc 0%, #fff8dc 100%);
            min-height: 100vh;
            padding: 20px;
            position: relative;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="gold" opacity="0.1"/></svg>') repeat;
            z-index: -1;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            padding: 40px;
            position: relative;
            overflow: hidden;
        }

        .container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, gold, #daa520, gold);
        }

        .bible-verse {
            background: linear-gradient(45deg, #f8f8ff, #e6e6fa);
            border: 2px solid #daa520;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .bible-verse::before {
            content: '🕊️';
            position: absolute;
            top: -10px;
            left: 50%;
            transform: translateX(-50%);
            background: white;
            padding: 5px 10px;
            font-size: 20px;
        }

        .verse-text {
            font-size: 18px;
            color: #4a4a4a;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .verse-reference {
            font-size: 14px;
            color: #666;
            font-style: italic;
        }

        .main-title {
            text-align: center;
            font-size: 36px;
            color: #2c3e50;
            margin: 30px 0;
            font-weight: 700;
            position: relative;
        }

        .main-title::before {
            content: '✝️';
            margin-left: 15px;
        }

        .main-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 3px;
            background: linear-gradient(90deg, transparent, gold, transparent);
        }

        .introduction {
            background: #f0f8ff;
            border-left: 5px solid #4682b4;
            padding: 20px;
            margin: 30px 0;
            border-radius: 10px;
            font-size: 16px;
            line-height: 1.6;
        }

        .exam-schedule {
            background: linear-gradient(135deg, #e3f2fd, #bbdefb);
            border: 2px solid #2196f3;
            border-radius: 15px;
            padding: 25px;
            margin: 30px 0;
            text-align: center;
        }

        .schedule-title {
            font-size: 24px;
            color: #1976d2;
            margin-bottom: 20px;
            font-weight: 700;
        }

        .schedule-item {
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 15px 0;
            font-size: 18px;
            color: #333;
        }

        .schedule-item .icon {
            margin-left: 10px;
            font-size: 20px;
        }

        .warning {
            background: #fff3cd;
            border: 2px solid #ffc107;
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
            color: #856404;
            font-weight: 600;
            text-align: center;
        }

        .motivational-question {
            background: linear-gradient(45deg, #fff5ee, #ffeee6);
            border: 2px solid #ff6b35;
            border-radius: 15px;
            padding: 25px;
            margin: 30px 0;
            text-align: center;
            font-size: 18px;
            color: #d2691e;
            font-weight: 600;
        }

        .notes-section {
            background: #f8f9fa;
            border: 2px solid #6c757d;
            border-radius: 15px;
            padding: 25px;
            margin: 30px 0;
        }

        .notes-title {
            font-size: 20px;
            color: #495057;
            margin-bottom: 20px;
            font-weight: 700;
            text-align: center;
        }

        .notes-list {
            list-style: none;
            padding: 0;
        }

        .notes-list li {
            padding: 10px 0;
            border-bottom: 1px solid #dee2e6;
            font-size: 16px;
            color: #495057;
        }

        .notes-list li:last-child {
            border-bottom: none;
        }

        .notes-list li::before {
            content: '📌';
            margin-left: 10px;
        }

        .final-message {
            background: linear-gradient(135deg, #e8f5e8, #d4edda);
            border: 2px solid #28a745;
            border-radius: 15px;
            padding: 25px;
            margin: 30px 0;
            text-align: center;
            font-size: 18px;
            color: #155724;
            font-weight: 600;
            position: relative;
        }

        .final-message::before {
            content: '✨';
            position: absolute;
            top: -10px;
            left: 50%;
            transform: translateX(-50%);
            background: white;
            padding: 5px 10px;
            font-size: 20px;
        }

        .exam-button {
            display: block;
            width: 300px;
            margin: 40px auto;
            padding: 15px 30px;
            background: linear-gradient(45deg, #28a745, #34ce57);
            color: white;
            border: none;
            border-radius: 25px;
            font-size: 20px;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .exam-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
        }

        .exam-button:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .exam-button::before {
            content: '📖';
            margin-left: 10px;
        }

        .decoration-icons {
            position: absolute;
            top: 20px;
            right: 20px;
            font-size: 24px;
            opacity: 0.3;
        }

        .decoration-icons span {
            margin: 0 10px;
            animation: float 3s ease-in-out infinite;
        }

        .decoration-icons span:nth-child(2) {
            animation-delay: 1s;
        }

        .decoration-icons span:nth-child(3) {
            animation-delay: 2s;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .time-display {
            text-align: center;
            font-size: 18px;
            color: #666;
            margin: 20px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
        }

        @media (max-width: 600px) {
            .container {
                padding: 20px;
                margin: 10px;
            }
            
            .main-title {
                font-size: 28px;
            }
            
            .exam-button {
                width: 250px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="decoration-icons">
            <span>⏰</span>
            <span>✝️</span>
            <span>📖</span>
        </div>

        <div class="bible-verse">
            <div class="verse-text">
                "افحصني يا الله واعرف قلبي. امتحني واعرف أفكاري."
            </div>
            <div class="verse-reference">
                (مزمور 139: 23)
            </div>
        </div>

        <h1 class="main-title">استعد للامتحان... قلبًا وعقلاً</h1>

        <div class="introduction">
            📘 هذه الصفحة مخصصة للطلبة المسيحيين فقط. يرجى تجهيز نفسك بهدوء وصلاة قبل الدخول إلى الامتحان. الرب معك في كل خطوة 🙏
        </div>

        <div class="exam-schedule">
            <div class="schedule-title">📅 موعد الامتحان</div>
            <div class="schedule-item">
                <span class="icon">📅</span>
                <span>تاريخ اليوم: 7/6/2025</span>
            </div>
            <div class="schedule-item">
                <span class="icon">📚</span>
                <span>عدد الأسئلة: 3 أسئلة</span>
            </div>
            <div class="schedule-item">
                <span class="icon">⏰</span>
                <span>الامتحان متاح الآن</span>
            </div>
            <div class="warning">
                ⚠️ لن يمكن الرجوع بعد البدء في الامتحان.
            </div>
        </div>

        <div class="time-display" id="current-time">
            الوقت الحالي: <span id="clock"></span>
        </div>

        <div class="motivational-question">
            ❓ هل أنت مستعد أن تُظهر معرفتك، وإيمانك، وثقتك؟<br>
            هذا الامتحان ليس فقط اختبارًا للمعلومات، بل هو شهادة لإيمانك أيضًا.
        </div>

        <div class="notes-section">
            <div class="notes-title">📌 ملاحظات مهمة</div>
            <ul class="notes-list">
                <li>تأكد من اتصال الإنترنت</li>
                <li>افتح الامتحان من جهازك الشخصي فقط</li>
                <li>لا تشارك الرابط مع أي شخص آخر</li>
                <li>وقت الامتحان محدود، ولن يمكن الرجوع بعد الخروج</li>
            </ul>
        </div>

        <div class="final-message">
            الرب يراك ويعرف تعبك... ثق أنه سيكون معك، يرشدك، ويذكّرك، ويقوّيك.<br>
            <strong>بالتوفيق يا بطل الإيمان!</strong>
        </div>

        <button class="exam-button" id="exam-button" disabled>
            ابدأ الامتحان
        </button>
    </div>

    <script>
        // عرض الوقت الحالي
        function updateClock() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('ar-SA', {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: true
            });
            document.getElementById('clock').textContent = timeString;
        }

        // تحديث الوقت كل ثانية
        setInterval(updateClock, 1000);
        updateClock();

        // تفعيل الامتحان فوراً
        function initializeExam() {
            const examButton = document.getElementById('exam-button');
            examButton.disabled = false;
            examButton.textContent = '📖 ابدأ الامتحان';
            examButton.style.background = 'linear-gradient(45deg, #28a745, #34ce57)';
        }

        // تفعيل الامتحان عند تحميل الصفحة
        initializeExam();

        // إضافة وظيفة للزر
        document.getElementById('exam-button').addEventListener('click', function() {
            if (!this.disabled) {
                // بدء الامتحان
                localStorage.setItem('examStarted', 'true');
                localStorage.setItem('currentQuestion', '1');
                localStorage.setItem('answers', JSON.stringify({}));
                localStorage.setItem('score', '0');
                window.location.href = 'question1.html';
            }
        });
    </script>
</body>
</html>