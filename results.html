<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نتائج الامتحان - امتحان الكتاب المقدس</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Amiri', serif;
            background: linear-gradient(135deg, #f5f5dc 0%, #fff8dc 100%);
            min-height: 100vh;
            padding: 20px;
            position: relative;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="gold" opacity="0.1"/></svg>') repeat;
            z-index: -1;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            padding: 40px;
            position: relative;
            overflow: hidden;
        }

        .container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, gold, #daa520, gold);
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .results-title {
            font-size: 36px;
            color: #2c3e50;
            margin-bottom: 20px;
            font-weight: 700;
        }

        .congratulations {
            font-size: 20px;
            color: #27ae60;
            margin-bottom: 20px;
        }

        .score-card {
            background: linear-gradient(135deg, #e8f5e9, #d4edda);
            border: 3px solid #28a745;
            border-radius: 20px;
            padding: 30px;
            margin: 30px 0;
            text-align: center;
            position: relative;
        }

        .score-card::before {
            content: '🏆';
            position: absolute;
            top: -15px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 30px;
            background: white;
            padding: 5px 10px;
            border-radius: 50%;
        }

        .score-display {
            font-size: 48px;
            color: #155724;
            font-weight: 700;
            margin-bottom: 15px;
        }

        .score-details {
            font-size: 18px;
            color: #155724;
            margin-bottom: 20px;
        }

        .percentage {
            font-size: 24px;
            color: #28a745;
            font-weight: 600;
        }

        .grade-section {
            background: #fff3cd;
            border: 2px solid #ffc107;
            border-radius: 15px;
            padding: 25px;
            margin: 30px 0;
            text-align: center;
        }

        .grade-title {
            font-size: 24px;
            color: #856404;
            margin-bottom: 15px;
            font-weight: 700;
        }

        .grade-value {
            font-size: 36px;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .grade-excellent {
            color: #155724;
        }

        .grade-good {
            color: #0c5460;
        }

        .grade-fair {
            color: #856404;
        }

        .grade-poor {
            color: #721c24;
        }

        .answers-review {
            background: #f8f9fa;
            border: 2px solid #6c757d;
            border-radius: 15px;
            padding: 25px;
            margin: 30px 0;
        }

        .review-title {
            font-size: 24px;
            color: #495057;
            margin-bottom: 20px;
            font-weight: 700;
            text-align: center;
        }

        .question-review {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
            border-left: 5px solid #6c757d;
        }

        .question-review.correct {
            border-left-color: #28a745;
            background: #f8fff9;
        }

        .question-review.incorrect {
            border-left-color: #dc3545;
            background: #fff5f5;
        }

        .question-text {
            font-size: 18px;
            color: #333;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .answer-line {
            font-size: 16px;
            margin: 5px 0;
        }

        .your-answer {
            color: #495057;
        }

        .correct-answer {
            color: #28a745;
            font-weight: 600;
        }

        .incorrect-answer {
            color: #dc3545;
            font-weight: 600;
        }

        .biblical-message {
            background: linear-gradient(135deg, #e3f2fd, #bbdefb);
            border: 2px solid #2196f3;
            border-radius: 15px;
            padding: 25px;
            margin: 30px 0;
            text-align: center;
            position: relative;
        }

        .biblical-message::before {
            content: '✝️';
            position: absolute;
            top: -10px;
            left: 50%;
            transform: translateX(-50%);
            background: white;
            padding: 5px 10px;
            font-size: 20px;
        }

        .bible-verse {
            font-size: 18px;
            color: #1976d2;
            font-weight: 600;
            margin-bottom: 15px;
        }

        .bible-reference {
            font-size: 14px;
            color: #666;
            font-style: italic;
        }

        .action-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            margin-top: 40px;
        }

        .action-button {
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            color: white;
            display: inline-block;
        }

        .retry-button {
            background: linear-gradient(45deg, #17a2b8, #138496);
        }

        .retry-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(23, 162, 184, 0.4);
        }

        .home-button {
            background: linear-gradient(45deg, #28a745, #218838);
        }

        .home-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
        }

        @media (max-width: 600px) {
            .container {
                padding: 20px;
                margin: 10px;
            }
            
            .results-title {
                font-size: 28px;
            }
            
            .score-display {
                font-size: 36px;
            }
            
            .action-buttons {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="results-title">🎓 نتائج الامتحان</div>
            <div class="congratulations">تم إنجاز الامتحان بنجاح!</div>
        </div>

        <div class="score-card">
            <div class="score-display" id="score-display">-/-</div>
            <div class="score-details" id="score-details">الإجابات الصحيحة من إجمالي الأسئلة</div>
            <div class="percentage" id="percentage">النسبة المئوية: -%</div>
        </div>

        <div class="grade-section">
            <div class="grade-title">التقدير</div>
            <div class="grade-value" id="grade">-</div>
        </div>

        <div class="answers-review">
            <div class="review-title">📝 مراجعة الإجابات</div>
            <div id="answers-container">
                <!-- سيتم إضافة الإجابات هنا بواسطة JavaScript -->
            </div>
        </div>

        <div class="biblical-message">
            <div class="bible-verse">
                "كل كتاب موحى به من الله، ونافع للتعليم والتوبيخ، للتقويم والتأديب الذي في البر"
            </div>
            <div class="bible-reference">
                (2 تيموثاوس 3: 16)
            </div>
        </div>

        <div class="action-buttons">
            <a href="exam_page.html" class="action-button retry-button">إعادة الامتحان</a>
            <a href="exam_page.html" class="action-button home-button">العودة للرئيسية</a>
        </div>
    </div>

    <script>
        // بيانات الأسئلة والإجابات الصحيحة
        const questionsData = [
            {
                number: 1,
                text: "من هو التلميذ الذي أنكر المسيح ثلاث مرات؟",
                answers: {"أ": "يوحنا", "ب": "بطرس", "ج": "توما", "د": "متى"},
                correct: "ب"
            },
            {
                number: 2,
                text: "في أي موضع وُلد السيد المسيح؟",
                answers: {"أ": "أورشليم", "ب": "الناصرة", "ج": "بيت لحم", "د": "قانا"},
                correct: "ج"
            },
            {
                number: 3,
                text: "ما اسم الجبل الذي صُلب عليه السيد المسيح؟",
                answers: {"أ": "جبل الزيتون", "ب": "جبل التجلي", "ج": "جلجثة", "د": "الكرمل"},
                correct: "ج"
            }
        ];

        // حساب النتائج
        function calculateResults() {
            const score = parseInt(localStorage.getItem('score') || '0');
            const userAnswers = JSON.parse(localStorage.getItem('answers') || '{}');
            const totalQuestions = questionsData.length;
            
            // حساب النسبة المئوية
            const percentage = Math.round((score / totalQuestions) * 100);
            
            // تحديد التقدير
            let grade, gradeClass;
            if (percentage >= 90) {
                grade = "ممتاز";
                gradeClass = "grade-excellent";
            } else if (percentage >= 80) {
                grade = "جيد جداً";
                gradeClass = "grade-good";
            } else if (percentage >= 70) {
                grade = "جيد";
                gradeClass = "grade-good";
            } else if (percentage >= 60) {
                grade = "مقبول";
                gradeClass = "grade-fair";
            } else {
                grade = "ضعيف";
                gradeClass = "grade-poor";
            }
            
            // عرض النتائج
            document.getElementById('score-display').textContent = `${score}/${totalQuestions}`;
            document.getElementById('score-details').textContent = `${score} إجابة صحيحة من ${totalQuestions} أسئلة`;
            document.getElementById('percentage').textContent = `النسبة المئوية: ${percentage}%`;
            
            const gradeElement = document.getElementById('grade');
            gradeElement.textContent = grade;
            gradeElement.className = `grade-value ${gradeClass}`;
            
            // عرض مراجعة الإجابات
            displayAnswersReview(userAnswers);
        }

        // عرض مراجعة الإجابات
        function displayAnswersReview(userAnswers) {
            const container = document.getElementById('answers-container');
            container.innerHTML = '';
            
            questionsData.forEach(question => {
                const userAnswer = userAnswers[question.number] || 'لم يجب';
                const isCorrect = userAnswer === question.correct;
                
                const reviewDiv = document.createElement('div');
                reviewDiv.className = `question-review ${isCorrect ? 'correct' : 'incorrect'}`;
                
                reviewDiv.innerHTML = `
                    <div class="question-text">
                        ${question.number}. ${question.text}
                    </div>
                    <div class="answer-line your-answer">
                        إجابتك: ${userAnswer === 'لم يجب' ? 'لم يجب' : userAnswer + ') ' + question.answers[userAnswer]}
                        ${isCorrect ? '✅' : '❌'}
                    </div>
                    ${!isCorrect ? `<div class="answer-line correct-answer">
                        الإجابة الصحيحة: ${question.correct}) ${question.answers[question.correct]} ✅
                    </div>` : ''}
                `;
                
                container.appendChild(reviewDiv);
            });
        }

        // التحقق من وجود بيانات الامتحان
        if (localStorage.getItem('examStarted') !== 'true') {
            alert('لم يتم العثور على بيانات الامتحان!');
            window.location.href = 'exam_page.html';
        } else {
            calculateResults();
        }

        // تنظيف البيانات عند مغادرة الصفحة
        window.addEventListener('beforeunload', function() {
            localStorage.removeItem('examStarted');
            localStorage.removeItem('answers');
            localStorage.removeItem('score');
            localStorage.removeItem('currentQuestion');
        });
    </script>
</body>
</html>